# 📦 Leantime NativePHP桌面端打包方案

## 📋 文档概述

本文档为Leantime项目转换为NativePHP桌面应用的完整打包方案，涵盖从开发环境到生产部署的全流程指导。

---

## 🎯 打包目标与策略

### 目标平台支持
- **Windows** (Windows 10/11, x64)
- **macOS** (macOS 11+, Intel & Apple Silicon)
- **Linux** (Ubuntu 20.04+, CentOS 8+)

### 打包策略概览
- **单一代码库** - 一套代码支持多平台
- **自动化构建** - CI/CD集成自动打包
- **增量更新** - 支持应用自动更新
- **数字签名** - 确保应用安全性

### 开发版 vs 生产版对比

| 特性类别 | 开发版 (Development) | 生产版 (Production) |
|----------|---------------------|-------------------|
| **调试功能** | ✅ 开发者工具、热重载、详细日志 | ❌ 禁用调试功能 |
| **代码优化** | ❌ 未压缩，保留源码映射 | ✅ 代码压缩、混淆 |
| **启动速度** | 较慢 (3-8秒) | 快速 (1-3秒) |
| **应用大小** | 较大 (~350MB) | 优化后 (~240MB) |
| **错误处理** | 详细错误信息 | 用户友好提示 |
| **更新机制** | 手动更新 | 自动更新 |
| **代码签名** | 可选 | 必需 |
| **性能监控** | 开发指标 | 生产监控 |

---

## 🏗️ 打包架构设计

### 应用架构层次

```
┌─────────────────────────────────────┐
│           用户界面层                  │
│    (Electron WebView + HTML/CSS)    │
├─────────────────────────────────────┤
│           业务逻辑层                  │
│      (Laravel + PHP Runtime)        │
├─────────────────────────────────────┤
│           数据存储层                  │
│         (SQLite Database)           │
├─────────────────────────────────────┤
│           系统集成层                  │
│    (Native APIs + File System)      │
└─────────────────────────────────────┘
```

### 打包组件构成

| 组件类型 | 组件名称 | 大小估算 | 作用描述 |
|----------|----------|----------|----------|
| **核心运行时** | PHP Runtime | ~50MB | 嵌入式PHP执行环境 |
| **应用框架** | Laravel Framework | ~30MB | Web应用框架 |
| **用户界面** | Electron Shell | ~120MB | 桌面应用容器 |
| **业务代码** | Leantime Source | ~25MB | 项目管理业务逻辑 |
| **数据库** | SQLite Engine | ~2MB | 本地数据存储 |
| **资源文件** | Assets & Media | ~15MB | 图片、字体、样式 |
| **总计** | **完整应用包** | **~240MB** | 跨平台桌面应用 |

---

## 🛠️ 开发阶段打包配置

### 开发版打包特点

#### 快速迭代优化
- **热重载支持** - 代码修改后自动刷新应用
- **源码映射** - 保留完整的调试信息
- **开发者工具** - 内置Chrome DevTools
- **详细日志** - 完整的错误堆栈和调试信息

#### 开发环境配置示例

```
开发版配置重点
├── 调试模式启用
│   ├── DevTools: enabled
│   ├── Hot Reload: enabled
│   ├── Source Maps: enabled
│   └── Verbose Logging: enabled
├── 性能要求放宽
│   ├── 启动时间: 可接受较慢
│   ├── 内存使用: 可适当增加
│   ├── 包体大小: 优先开发效率
│   └── 代码压缩: 禁用
├── 安全要求降低
│   ├── 代码签名: 可选
│   ├── 证书验证: 跳过
│   ├── 更新检查: 禁用
│   └── 错误上报: 本地处理
└── 开发工具集成
    ├── 自动重启: 文件变更触发
    ├── 调试端口: 开放调试接口
    ├── 测试数据: 内置测试数据
    └── 开发菜单: 额外开发选项
```

### 开发阶段工作流程

```
开发迭代循环
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   代码修改       │───▶│   自动构建       │───▶│   应用重载       │
│   Code Changes  │    │   Auto Build    │    │   App Reload    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                        │                        │
         │                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   继续开发       │◀───│   问题修复       │◀───│   功能测试       │
│   Continue Dev  │    │   Bug Fixes     │    │   Feature Test  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 开发版打包命令

| 操作类型 | 命令示例 | 用途说明 |
|----------|----------|----------|
| **快速开发** | `php artisan native:serve --dev` | 启动开发服务器，支持热重载 |
| **调试构建** | `php artisan native:build --debug` | 构建调试版本，保留调试信息 |
| **测试打包** | `php artisan native:build --test` | 构建测试版本，用于功能验证 |
| **预览构建** | `php artisan native:build --preview` | 构建预览版本，接近生产环境 |

---

## 🔄 打包流程设计

### 开发版 vs 生产版流程对比

```
开发版流程 (快速迭代)
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  代码修改    │───▶│  快速构建    │───▶│  本地测试    │
│             │    │ (未优化)     │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                                      │
       └──────────────── 迭代循环 ──────────────┘

生产版流程 (完整发布)
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  代码冻结    │───▶│  完整构建    │───▶│  全面测试    │───▶│  正式发布    │
│             │    │ (全优化)     │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### 完整打包流程图

```mermaid
graph TD
    A[开发环境准备] --> B{构建类型选择}
    B -->|开发版| B1[快速构建流程]
    B -->|生产版| B2[完整构建流程]

    B1 --> C1[代码检查 - 基础]
    C1 --> D1[依赖安装 - 开发]
    D1 --> E1[资源处理 - 未压缩]
    E1 --> F1[配置生成 - 开发]
    F1 --> G1[单平台构建]
    G1 --> H1[本地测试]

    B2 --> C2[代码检查 - 严格]
    C2 --> D2[依赖优化 - 生产]
    D2 --> E2[资源压缩 - 优化]
    E2 --> F2[配置生成 - 生产]
    F2 --> G2[多平台构建]
    G2 --> H2[应用签名]
    H2 --> I2[安装包生成]
    I2 --> J2[质量测试]
    J2 --> K2[发布部署]
```

### 关键阶段详解

#### 开发阶段重点 (当前阶段)

**阶段1：开发环境搭建 (1天)**
- **NativePHP安装** - `composer require nativephp/electron`
- **开发工具配置** - 热重载、调试工具
- **基础依赖检查** - PHP、Node.js版本确认
- **开发数据库** - SQLite开发环境配置

**阶段2：功能开发迭代 (持续)**
- **快速构建** - `php artisan native:serve --dev`
- **功能测试** - 单个功能模块验证
- **界面调试** - UI/UX实时调整
- **性能监控** - 开发阶段性能基准

**阶段3：阶段性验证 (每周)**
- **功能集成测试** - 多模块协作验证
- **跨平台兼容** - 主要平台基础测试
- **用户体验评估** - 内部团队反馈收集
- **技术债务清理** - 代码质量维护

#### 生产发布阶段 (未来规划)

**阶段1：代码优化 (3-5天)**
- **性能优化** - 代码压缩、资源优化
- **兼容性处理** - 跨平台API适配
- **安全加固** - 敏感信息保护

**阶段2：构建打包 (2-3天)**
- **自动化构建** - CI/CD流水线配置
- **多平台编译** - 并行构建不同平台版本
- **质量验证** - 自动化测试和验收

**阶段3：发布部署 (1-2天)**
- **数字签名** - 应用可信度认证
- **分发渠道** - 官网、应用商店、企业内部
- **更新机制** - 自动更新服务配置

---

## 🛠️ 构建环境配置

### 开发环境要求

| 环境类型 | 最低配置 | 推荐配置 | 备注 |
|----------|----------|----------|------|
| **操作系统** | Windows 10/macOS 11/Ubuntu 20.04 | 最新稳定版 | 支持跨平台开发 |
| **内存** | 8GB RAM | 16GB+ RAM | 构建过程内存密集 |
| **存储** | 50GB 可用空间 | 100GB+ SSD | 多平台构建需要空间 |
| **网络** | 稳定互联网连接 | 高速宽带 | 下载依赖和上传构建产物 |

### 必需软件工具

```
开发工具链
├── PHP 8.2+
├── Composer 2.0+
├── Node.js 18.0+
├── NPM/Yarn
├── Git 2.30+
└── Docker (可选)

构建工具
├── Electron Builder
├── NativePHP CLI
├── Webpack/Vite
└── 平台特定工具
    ├── Windows: Visual Studio Build Tools
    ├── macOS: Xcode Command Line Tools
    └── Linux: Build Essential
```

---

## 📊 打包配置策略

### 配置文件层次结构

```
配置管理 (开发阶段重点)
├── 基础配置 (base.json)
│   ├── 应用基本信息
│   ├── 通用构建选项
│   └── 共享资源定义
├── 环境配置 ⭐ 当前重点
│   ├── 开发环境 (development.json) ← 主要使用
│   ├── 测试环境 (staging.json) ← 阶段验证
│   └── 生产环境 (production.json) ← 未来准备
├── 平台配置 (简化版)
│   ├── Windows (windows-dev.json) ← 开发简化版
│   ├── macOS (macos-dev.json) ← 开发简化版
│   └── Linux (linux-dev.json) ← 开发简化版
└── 开发专用配置 ⭐ 新增
    ├── 调试配置 (debug.json)
    ├── 热重载配置 (hot-reload.json)
    └── 开发工具配置 (dev-tools.json)
```

### 开发阶段配置重点

| 配置项 | 开发版设置 | 生产版设置 | 说明 |
|--------|------------|------------|------|
| **调试模式** | `debug: true` | `debug: false` | 开启/关闭调试功能 |
| **热重载** | `hotReload: true` | `hotReload: false` | 代码变更自动刷新 |
| **源码映射** | `sourceMap: true` | `sourceMap: false` | 调试时显示原始代码 |
| **代码压缩** | `minify: false` | `minify: true` | 开发时保持可读性 |
| **错误详情** | `verboseErrors: true` | `verboseErrors: false` | 详细错误信息 |
| **开发工具** | `devTools: true` | `devTools: false` | Chrome DevTools |
| **自动更新** | `autoUpdate: false` | `autoUpdate: true` | 开发时手动控制 |

### 开发阶段关键配置

| 配置类别 | 参数名称 | 开发版配置 | 生产版配置 | 当前建议 |
|----------|----------|------------|------------|----------|
| **应用信息** | appId | com.leantime.desktop.dev | com.leantime.desktop | 使用开发版ID |
| **应用信息** | productName | Leantime Desktop (Dev) | Leantime Desktop | 明确标识开发版 |
| **应用信息** | version | 0.1.0-dev | 1.0.0 | 开发版本号 |
| **构建选项** | compression | none | maximum | 开发时不压缩 |
| **构建选项** | asar | false | true | 开发时不打包 |
| **调试选项** | devTools | true | false | 开启开发工具 |
| **调试选项** | hotReload | true | false | 启用热重载 |
| **更新机制** | publish | disabled | enabled | 开发时禁用 |
| **安全选项** | codeSign | false | true | 开发时可跳过 |

### 开发环境快速配置示例

```json
// config/nativephp-development.json
{
  "appId": "com.leantime.desktop.dev",
  "productName": "Leantime Desktop (Development)",
  "version": "0.1.0-dev",
  "main": "public/index.php",
  "build": {
    "compression": "store",
    "asar": false,
    "directories": {
      "output": "dist-dev"
    }
  },
  "development": {
    "devTools": true,
    "hotReload": true,
    "sourceMap": true,
    "verboseLogging": true
  },
  "window": {
    "width": 1400,
    "height": 900,
    "webPreferences": {
      "devTools": true,
      "contextIsolation": false
    }
  }
}
```

---

## 🔐 安全与签名策略

### 代码签名重要性

```
代码签名价值链
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   开发者身份     │───▶│   应用完整性     │───▶│   用户信任度     │
│   Developer ID   │    │   Code Integrity │    │   User Trust    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
   防止身份伪造              防止代码篡改              减少安全警告
```

### 平台签名要求

#### Windows平台
- **证书类型**: EV代码签名证书 (推荐) 或标准代码签名证书
- **签名工具**: SignTool.exe
- **时间戳**: 必须添加时间戳服务器
- **SmartScreen**: EV证书可立即获得信誉

#### macOS平台
- **开发者账号**: Apple Developer Program ($99/年)
- **证书类型**: Developer ID Application
- **公证流程**: 必须通过Apple公证服务
- **Gatekeeper**: 确保系统安全检查通过

#### Linux平台
- **GPG签名**: 使用GPG密钥签名软件包
- **仓库分发**: 通过可信软件仓库分发
- **校验和**: 提供SHA256校验和文件

---

## 📈 性能优化策略

### 应用启动优化

```
启动性能优化路径
┌─────────────────┐
│   冷启动 (3-5s)  │
└─────────┬───────┘
          │ 优化后
          ▼
┌─────────────────┐
│   热启动 (1-2s)  │
└─────────┬───────┘
          │ 进一步优化
          ▼
┌─────────────────┐
│  即时启动 (<1s)  │
└─────────────────┘
```

### 优化技术手段

| 优化类别 | 技术手段 | 性能提升 | 实施难度 |
|----------|----------|----------|----------|
| **代码优化** | PHP OPcache | 30-50% | 低 |
| **资源优化** | 静态资源压缩 | 20-30% | 低 |
| **缓存策略** | 应用级缓存 | 40-60% | 中 |
| **数据库优化** | SQLite调优 | 25-40% | 中 |
| **预加载** | 关键组件预加载 | 35-50% | 高 |
| **懒加载** | 按需加载模块 | 20-35% | 中 |

### 内存使用优化

```
内存使用分布
┌─────────────────────────────────────┐
│ PHP Runtime     │████████████ 40%   │
│ Electron Shell  │██████████ 35%     │
│ Application Code│████ 15%           │
│ Database Cache  │██ 7%              │
│ Other Resources │█ 3%               │
└─────────────────────────────────────┘
总内存使用: ~400MB (优化后)
```

---

## 🚀 部署与分发策略

### 分发渠道矩阵

| 分发渠道 | 适用场景 | 优势 | 劣势 | 推荐度 |
|----------|----------|------|------|--------|
| **官方网站** | 通用下载 | 完全控制、无审核 | 需要自建基础设施 | ⭐⭐⭐⭐⭐ |
| **Microsoft Store** | Windows用户 | 官方认证、自动更新 | 审核严格、分成30% | ⭐⭐⭐⭐ |
| **Mac App Store** | macOS用户 | 系统集成、安全性高 | 审核严格、功能限制 | ⭐⭐⭐ |
| **Snap Store** | Linux用户 | 跨发行版、自动更新 | 性能开销、兼容性 | ⭐⭐⭐ |
| **企业内部** | 企业客户 | 定制化、安全可控 | 维护成本高 | ⭐⭐⭐⭐ |

### 更新机制设计

```
自动更新流程
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   检查更新       │───▶│   下载更新       │───▶│   安装更新       │
│   Check Update  │    │   Download      │    │   Install       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
   版本比较逻辑              增量下载优化              静默安装重启
```

### 版本管理策略

#### 语义化版本控制
- **主版本号** (Major): 不兼容的API修改
- **次版本号** (Minor): 向下兼容的功能性新增
- **修订号** (Patch): 向下兼容的问题修正

#### 发布周期规划
- **稳定版** (Stable): 每季度发布，经过充分测试
- **预览版** (Preview): 每月发布，包含新功能预览
- **热修复** (Hotfix): 按需发布，修复严重问题

---

## 📊 质量保证体系

### 测试金字塔

```
测试层次结构
        ┌─────────────────┐
        │   E2E测试 (10%)  │  ← 用户场景测试
        └─────────────────┘
      ┌─────────────────────┐
      │  集成测试 (20%)      │    ← 组件协作测试
      └─────────────────────┘
    ┌─────────────────────────┐
    │    单元测试 (70%)        │      ← 功能逻辑测试
    └─────────────────────────┘
```

### 质量检查清单

#### 功能性测试
- [ ] 核心业务功能完整性
- [ ] 用户界面响应性
- [ ] 数据持久化正确性
- [ ] 跨平台兼容性

#### 性能测试
- [ ] 应用启动时间 < 3秒
- [ ] 内存使用 < 500MB
- [ ] CPU使用率合理
- [ ] 磁盘I/O优化

#### 安全测试
- [ ] 代码签名验证
- [ ] 数据加密保护
- [ ] 权限控制检查
- [ ] 漏洞扫描通过

#### 用户体验测试
- [ ] 界面美观一致
- [ ] 操作流程顺畅
- [ ] 错误处理友好
- [ ] 帮助文档完整

---

## 📋 项目里程碑

### 项目时间表 (开发阶段重点)

```
开发阶段时间线 (当前重点: 前6周)
┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐
│ W1  │ W2  │ W3  │ W4  │ W5  │ W6  │ W7  │ W8  │ W9  │ W10 │ W11 │ W12 │
├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤
│🔧环境│📝代码│🗄️数据│⚙️配置│🏗️构建│🧪测试│⚡优化│🔐签名│📦分发│✅验收│🚀发布│🛠️维护│
│ 搭建 │ 迁移 │ 迁移 │ 调试 │ 打包 │ 验证 │ 性能 │ 认证 │ 部署 │ 测试 │ 上线 │ 支持 │
├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤
│ 开发 │ 开发 │ 开发 │ 开发 │ 开发 │ 开发 │ 生产 │ 生产 │ 生产 │ 生产 │ 生产 │ 生产 │
│ 重点 │ 重点 │ 重点 │ 重点 │ 重点 │ 重点 │ 准备 │ 准备 │ 准备 │ 准备 │ 准备 │ 准备 │
└─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘
```

### 开发阶段详细规划

#### 当前阶段 (W1-W6): 开发版打包重点

**第1-2周：基础环境**
- ✅ NativePHP开发环境搭建
- ✅ 开发版配置文件创建
- ✅ 热重载和调试工具配置
- ✅ 基础功能验证

**第3-4周：核心功能**
- 🔄 业务逻辑迁移和适配
- 🔄 数据库迁移到SQLite
- 🔄 界面适配和优化
- 🔄 开发版打包测试

**第5-6周：功能完善**
- ⏳ 功能模块集成测试
- ⏳ 跨平台兼容性验证
- ⏳ 性能基准测试
- ⏳ 开发版稳定性验证

#### 未来阶段 (W7-W12): 生产版准备

**第7-8周：生产优化**
- 📋 代码压缩和优化
- 📋 资源文件优化
- 📋 安全加固
- 📋 性能调优

**第9-10周：发布准备**
- 📋 代码签名配置
- 📋 自动更新机制
- 📋 分发渠道准备
- 📋 质量保证测试

**第11-12周：正式发布**
- 📋 生产版本发布
- 📋 用户反馈收集
- 📋 问题修复和优化
- 📋 持续维护支持

### 关键里程碑

#### 开发阶段里程碑 (当前重点)

| 里程碑 | 完成时间 | 交付物 | 验收标准 | 状态 |
|--------|----------|--------|----------|------|
| **M1: 开发环境就绪** | 第1周 | NativePHP开发环境 | `php artisan native:serve` 可运行 | 🎯 当前目标 |
| **M2: 基础功能迁移** | 第3周 | 核心模块适配 | 登录、项目、任务基础功能 | ⏳ 计划中 |
| **M3: 数据库迁移** | 第4周 | SQLite数据库 | 数据完整迁移，功能正常 | ⏳ 计划中 |
| **M4: 开发版打包** | 第5周 | 开发版应用包 | 本地可安装运行 | ⏳ 计划中 |
| **M5: 功能验证** | 第6周 | 功能测试报告 | 核心功能验证通过 | ⏳ 计划中 |

#### 生产发布里程碑 (未来规划)

| 里程碑 | 完成时间 | 交付物 | 验收标准 | 状态 |
|--------|----------|--------|----------|------|
| **M6: 性能优化** | 第8周 | 优化版本 | 启动时间<3秒，内存<500MB | 📋 未来计划 |
| **M7: 跨平台构建** | 第9周 | 多平台应用包 | Windows/macOS/Linux构建成功 | 📋 未来计划 |
| **M8: 代码签名** | 第10周 | 签名版本 | 通过系统安全检查 | 📋 未来计划 |
| **M9: 质量验收** | 第11周 | 测试报告 | 全面质量标准达标 | 📋 未来计划 |
| **M10: 正式发布** | 第12周 | 生产版本 | 用户可正常下载使用 | 📋 未来计划 |

### 开发阶段成功指标

#### 技术指标
- **构建成功率** > 95%
- **功能覆盖率** > 80% (核心功能)
- **启动时间** < 8秒 (开发版可接受)
- **内存使用** < 600MB (开发版可接受)

#### 开发效率指标
- **热重载响应** < 3秒
- **构建时间** < 5分钟
- **调试便利性** 开发工具完全可用
- **错误定位** 详细错误信息和堆栈

---

## 🎯 成功关键因素

### 技术成功要素
- **架构设计合理** - 模块化、可扩展的系统架构
- **性能优化到位** - 满足用户体验期望的响应速度
- **兼容性良好** - 跨平台一致的用户体验
- **安全性可靠** - 完善的安全防护机制

### 项目管理要素
- **团队技能匹配** - 具备相关技术栈经验的开发团队
- **时间规划合理** - 充分的开发和测试时间预留
- **质量控制严格** - 完善的测试和验收流程
- **风险管控有效** - 及时识别和应对潜在风险

### 用户接受要素
- **功能完整性** - 满足用户核心需求的功能覆盖
- **易用性良好** - 符合用户习惯的交互设计
- **稳定性可靠** - 低故障率的稳定运行
- **支持服务完善** - 及时响应的技术支持

---

## 📞 支持与维护

### 技术支持体系
- **文档支持** - 完整的用户手册和开发文档
- **社区支持** - 活跃的用户社区和开发者论坛
- **专业支持** - 企业级技术支持服务
- **培训服务** - 用户培训和开发者培训

### 持续维护计划
- **定期更新** - 功能增强和安全补丁
- **性能监控** - 应用性能和用户体验监控
- **用户反馈** - 收集和处理用户意见建议
- **技术演进** - 跟进技术发展和平台更新

---

*本文档将随着项目进展持续更新和完善*
