{% import "include/macros.twig" as macros %}
{% block content %}
---
title: {{ node.FullyQualifiedStructuralElementName }}
{% include 'config/frontmatter.twig' %}
---

# {{ node.name }}

{{ node.summary|raw }}

{{ node.description|raw }}
`{{ node.FullyQualifiedStructuralElementName }}`

{% if node.parent %}* Parent class: {{ macros.mdClassLink(node.parent, macros.mdClassPath(node), node.parent.FullyQualifiedStructuralElementName) }}
{% endif %}
{% if node.final %}* This class is marked as **final** and can't be subclassed
{% endif %}
{% if node.deprecated %}* **Warning:** this class is **deprecated**. This means that this class will likely be removed in a future version.
{% endif %}
{% if node.interfaces is not empty %}* This class implements: {{ node.interfaces|join(', ')|raw }}
{% endif %}

{% if node.tags.see is not empty or node.tags.link is not empty %}
**See Also:**

{% for see in node.tags.see %}
* {{ see.reference }} {% if see.description %}- {{ see.description|raw }}{% endif %}

{% endfor %}
{% for link in node.tags.link %}
* {{ link.link }} {% if link.description and link.description != link.link %}- {{ link.description|raw }}{% endif %}

{% endfor %}

{% endif %}{# node.tags.see || node.tags.link #}

{# Check constants #}
{% if node.constants|length > 0 %}

## Constants

| Constant | Type | Value |
|:---      |:---  |:---   |
{% for constant in node.constants %}
|`{{constant.FullyQualifiedStructuralElementName}}`|{{constant.type}}|{{constant.value}}|
{% endfor %}{# for constant #}
{% endif %}{# if constants #}

{# Check methods #}
{% if node.methods|length > 0 %}
## Methods

{% for method in node.methods %}
{% include 'method.md.twig' %}
{% endfor %}
{% endif %}{# if methods #}

{# Check inherited methods #}
{% if node.InheritedMethods|length > 0 %}

## Inherited methods

{% for method in node.InheritedMethods %}
{% include 'method.md.twig' %}
{% endfor %}

{% endif %}{# if InheritedMethods #}

{% include 'config/footer.md.twig' %}
{% endblock %}
