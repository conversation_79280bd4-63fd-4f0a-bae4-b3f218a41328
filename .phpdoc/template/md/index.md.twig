{% import "include/macros.twig" as macros %}
{% block content %}
---
title: {{project.name}}
{% include 'config/frontmatter.twig' %}
---

# {{ project.name|raw }}

This is an automatically generated documentation for **{{project.name}}**.

{# Gitlab supports automatic table of contents generation #}
{% if md_flavour == 'gitlab' %}
# Table of contents
[[_TOC_]]
{% endif %}

{% for namespace in project.indexes.namespaces|sort((a,b) => a.FullyQualifiedStructuralElementName <=> b.FullyQualifiedStructuralElementName) %}
{% if namespace.classes|length > 0 or namespace.traits|length > 0 or namespace.interfaces|length > 0 %}

## {{ namespace.FullyQualifiedStructuralElementName }}

{% if namespace.classes|length > 0 %}
### Classes

| Class | Description |
|---    |---          |
{% for class in namespace.classes|sort((a,b) => a.FullyQualifiedStructuralElementName <=> b.FullyQualifiedStructuralElementName) %}
| {{ macros.mdClassLink(class) }} | {{ class.summary|replace({'|': '&#124;'})|nl2br|replace({"\n": "", "\r": "", "\t": ""})|raw }}|
{% endfor %}{# for class #}
{% endif %}{# if classes.length #}

{% if namespace.traits|length > 0 %}

#### Traits

| Trait | Description |
|---    |---          |
{% for trait in namespace.traits|sort((a,b) => a.FullyQualifiedStructuralElementName <=> b.FullyQualifiedStructuralElementName) %}
| {{ macros.mdClassLink(trait) }} | {{ trait.summary|replace({'|': '&#124;'})|nl2br|replace({"\n": "", "\r": "", "\t": ""})|raw }}|
{% endfor %}{# for trait #}

{% endif %}{# if traits #}

{% if namespace.interfaces|length > 0 %}

#### Interfaces

| Interface | Description |
|---    |---          |
{% for interface in namespace.interfaces|sort((a,b) => a.FullyQualifiedStructuralElementName <=> b.FullyQualifiedStructuralElementName) %}
| {{ macros.mdClassLink(interface) }} | {{ interface.summary|replace({'|': '&#124;'})|nl2br|replace({"\n": "", "\r": "", "\t": ""})|raw }}|
{% endfor %}{# for interace #}

{% endif %}{# if interfaces #}

{% endif %}{# if classes, traits, interfaces length #}
{% endfor %}{# for namespace #}

{% include 'config/footer.md.twig' %}
{% endblock %}

#### Hooks

[Reference Here](hooks.md)
