name: Acceptance Tests (Selenium)

on:
  workflow_dispatch:
  push:
    branches: [ "master", "*.*-dev" ]
  pull_request:
    branches: [ "master", "*.*-dev" ]

jobs:
  acceptance:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v3

      - name: Run Acceptance Tests
        run: make acceptance-test-ci

      - name: Store screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: acceptance-test
          path: tests/_output
