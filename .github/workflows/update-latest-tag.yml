name: Updated Latest Tag

on:
  workflow_dispatch:

jobs:
  build_release:
    name: build_release
    runs-on: ubuntu-24.04
    steps:
      - name: checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0    
      - name: Run latest-tag
        uses: EndBug/latest-tag@latest
        with:
          # You can change the name of the tag or branch with this input.
          # Default: 'latest'
          ref: latest
        
          # If a description is provided, the action will use it to create an annotated tag. If none is given, the action will create a lightweight tag.
          # Default: ''
          description: Latest Release of Leantime. 
        
          # Force-update a branch instead of using a tag.
          # Default: false
          # force-branch: true
        
          # Directory to use when executing git commands
          # Default: '${{ github.workspace }}'
          # git-directory: 'path/to/repo/dir'
