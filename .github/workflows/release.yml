name: Create Release

on:
  workflow_dispatch:
  push:
    tags:
      - '*'

jobs:
  build_release:
    name: build_release
    runs-on: ubuntu-24.04
    steps:
      - name: checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: version
        run: echo "version=$(make get-version)" >> $GITHUB_OUTPUT
        id: version

      - name: Get Hash
        id: hash
        run: echo "git_hash=$(git rev-parse --short "$GITHUB_SHA")" >> $GITHUB_OUTPUT
      - name: build artifacts
        run: make package
        
      - name: Extract Repo Attributes
        id: attrs
        uses: ibnesayeed/repo-attrs@master
      
      - name: release
        uses: ncipollo/release-action@v1
        id: create_release
        with:
          draft: false
          prerelease: false
          name: Leantime v${{ steps.version.outputs.version }}
          tag: v${{ steps.version.outputs.version }}
          generateReleaseNotes: false
          makeLatest: true
          body: |
            ## Changes in this Release

            History from `${{ steps.attrs.outputs.tail }}` to `${{ steps.attrs.outputs.head }}`

            ### Commits

            ${{ steps.attrs.outputs.commits }}

            ### Contributors

            ${{ steps.attrs.outputs.contributors }}

            ### Files

            ```
            ${{ steps.attrs.outputs.files }}
            ```
        env:
          GITHUB_TOKEN: ${{ github.token }}
      
      - name: upload zip
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ github.token }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./target/Leantime-v${{ steps.version.outputs.version }}.zip
          asset_name: Leantime-v${{ steps.version.outputs.version }}.zip
          asset_content_type: application/gzip
      
      - name: upload tar
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ github.token }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./target/Leantime-v${{ steps.version.outputs.version }}.tar.gz
          asset_name: Leantime-v${{ steps.version.outputs.version }}.tar.gz
          asset_content_type: application/gzip
      
      - name: Run latest-tag
        uses: EndBug/latest-tag@latest
        with:
          # You can change the name of the tag or branch with this input.
          # Default: 'latest'
          ref: latest
        
          # If a description is provided, the action will use it to create an annotated tag. If none is given, the action will create a lightweight tag.
          # Default: ''
          description: Latest Release of Leantime. 
