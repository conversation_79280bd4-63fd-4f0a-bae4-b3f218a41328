name: Unit Tests

on:
  workflow_dispatch:
  push:
    branches: [ "master", "*.*-dev" ]
  pull_request:
    branches: [ "master", "*.*-dev" ]

jobs:
  unit:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v3

      - name: Run Unit Tests
        run: make unit-test

      - name: Store screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: unit-test
          path: tests/_output
