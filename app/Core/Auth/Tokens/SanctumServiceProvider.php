<?php

namespace Leantime\Core\Auth\Tokens;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Sanctum\Contracts\HasAbilities;
use <PERSON><PERSON>\Sanctum\Sanctum as SanctumBase;
use Leantime\Domain\Auth\Services\AccessToken;

class SanctumServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(HasAbilities::class, AccessToken::class);
    }

    public function boot(): void
    {

        // Use our custom token model
        SanctumBase::usePersonalAccessTokenModel(AccessToken::class);

    }
}
