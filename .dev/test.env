#
# This environment file is used for our testing framework
# It is not to be used for development
#

## Minimum Configuration, these are required for installation

APP_ENV=testing
APP_DEBUG=true
APP_KEY=base64:testing123testing123testing123testing123=
APP_URL=https://leantime-dev

LEAN_APP_URL = 'https://leantime-dev'                             # Base URL, only needed for subfolder installation
LEAN_APP_DIR = ''                             # Base of application without trailing slash (used for cookies), e.g, /leantime
LEAN_DEBUG = true                                # Debug flag

# Database
LEAN_DB_HOST=leantime-db
LEAN_DB_USER=root
LEAN_DB_PASSWORD=test
LEAN_DB_DATABASE=leantime_test
LEAN_DB_PORT=3306

## Optional Configuraiton, you may ommit these from your .env file

## Default Settings
LEAN_SITENAME = 'Leantime'                         # Name of your site, can be changed later
LEAN_LANGUAGE = 'en-US'                            # Default language
LEAN_DEFAULT_TIMEZONE = 'America/Los_Angeles'      # Set default timezone
LEAN_ENABLE_MENU_TYPE = false                      # Enable to specifiy menu on aproject by project basis
LEAN_SESSION_PASSWORD = '3evBlq9zdUEuzKvVJHWWx3QzsQhturBApxwcws2m'  #Salting sessions. Replace with a strong password
LEAN_SESSION_EXPIRATION = 28800                    # How many seconds after inactivity should we logout?  28800seconds = 8hours
LEAN_LOG_PATH = ''                                # Default Log Path (including filename), if not set /logs/error.log will be used

## Look & Feel, these settings are available in the UI and can be overwritten there.
LEAN_LOGO_PATH = '/dist/images/logo.svg'                # Default logo path, can be changed later
LEAN_PRINT_LOGO_URL = '/dist/images/logo.jpg'           # Default logo URL use for printing (must be jpg or png format)
LEAN_DEFAULT_THEME = 'default'                     # Default theme
LEAN_PRIMARY_COLOR = '#006d9f'                     # Primary Theme color
LEAN_SECONDARY_COLOR = '#81B1A8'                   # Secondary Theme Color
LEAN_KEEP_THEME = true                             # Keep theme and language from previous user for login screen

## Fileuploads

# Local File Uploads
LEAN_USER_FILE_PATH = 'userfiles/'                 # Local relative path to store uploaded files (if not using S3)
LEAN_DB_BACKUP_PATH = 'backupdb/'                  # Local relative path to store backup files, need permission to write

# S3 File Uploads
LEAN_USE_S3 = false                                # Set to true if you want to use S3 instead of local files
LEAN_S3_KEY = 'AKIAIOSFODNN7EXAMPLE'                                   # S3 Key, hardcoded in s3ninja
LEAN_S3_SECRET = 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'            # S3 Secret, hardcoded in s3ninja
LEAN_S3_BUCKET = 'leantime'                       # Your S3 bucket
LEAN_S3_USE_PATH_STYLE_ENDPOINT = true            # Sets the endpoint style: false => https://[bucket].[endpoint] ; true => https://[endpoint]/[bucket]
LEAN_S3_REGION = 'eu-west-1'                                # S3 region
LEAN_S3_FOLDER_NAME = ''                           # Foldername within S3 (can be emtpy)
LEAN_S3_END_POINT = "http://s3ninja:9000"               # S3 EndPoint S3 Compatible (https://sfo2.digitaloceanspaces.com)

## Email
LEAN_EMAIL_RETURN = '<EMAIL>'                             # Return email address, needs to be valid email address format
LEAN_EMAIL_USE_SMTP = true                        # Use SMTP? If set to false, the default php mail() function will be used
LEAN_EMAIL_SMTP_HOSTS = 'maildev'                         # SMTP host
LEAN_EMAIL_SMTP_AUTH = false                        # SMTP authentication required
LEAN_EMAIL_SMTP_USERNAME = ''                      # SMTP username
LEAN_EMAIL_SMTP_PASSWORD = ''                      # SMTP password
LEAN_EMAIL_SMTP_AUTO_TLS = true                    # SMTP Enable TLS encryption automatically if a server supports it
LEAN_EMAIL_SMTP_SECURE = ''                        # SMTP Security protocol (usually one of: TLS, SSL, STARTTLS)
LEAN_EMAIL_SMTP_SSLNOVERIFY = true                # SMTP Allow insecure SSL: Don't verify certificate, accept self-signed, etc.
LEAN_EMAIL_SMTP_PORT = '465'                          # Port (usually one of 25, 465, 587, 2526)

## Ldap
LEAN_LDAP_USE_LDAP = false                         # Set to true if you want to use LDAP
LEAN_LDAP_LDAP_TYPE = 'OL'                         # Select the correct directory type. Currently Supported: OL - OpenLdap, AD - Active Directory
LEAN_LDAP_HOST = ''                                # FQDN
LEAN_LDAP_PORT = 389                               # Default Port
LEAN_LDAP_DN = ''                                  # Location of users, example: CN=users,DC=example,DC=com

                                                    # Leantime->Ldap attribute mapping
LEAN_LDAP_KEYS = "{\"username\":\"uid\",\"groups\":\"memberOf\",\"email\":\"mail\",\"firstname\":\"displayname\",\"lastname\":\"\",\"phonenumber\":\"telephoneNumber\"}"

# For AD use these default attributes
# LEAN_LDAP_KEYS = "{
#        \"username\":\"cn\",
#        \"groups\":\"memberOf\",
#        \"email\":\"mail\",
#        \"firstname\":\"givenName\",
#        \"lastname\":\"sn\",
#        \"phonenumber\":\"telephoneNumber\"
#      }"

LEAN_LDAP_DEFAULT_ROLE_KEY = 20;                   # Default Leantime Role on creation. (set to editor)

# Default role assignments upon first login.
# (Optional) Can be updated later in user settings for each user
LEAN_LDAP_GROUP_ASSIGNMENT = "{\"5\": {\"ltRole\":\"readonly\",\"ldapRole\":\"readonly\"},\"10\": {\"ltRole\":\"commenter\",\"ldapRole\":\"commenter\"},\"20\": {\"ltRole\":\"editor\",\"ldapRole\":\"editor\"},\"30\": {\"ltRole\":\"manager\",\"ldapRole\":\"manager\"},\"40\": {\"ltRole\":\"admin\",\"ldapRole\":\"administrators\"},\"50\": {\"ltRole\":\"owner\",\"ldapRole\":\"administrators\"}}"


## OpenID Connect
# required
LEAN_OIDC_ENABLE = true
LEAN_OIDC_CLIEND_ID =
LEAN_OIDC_CLIEND_SECRET =

# required - the url for your provider (examples down below)
#LEAN_OIDC_PROVIDER_URL =

# optional - these will be read from the well-known configuration if possible
#LEAN_OIDC_AUTH_URL_OVERRIDE =
#LEAN_OIDC_TOKEN_URL_OVERRIDE =
#LEAN_OIDC_JWKS_URL_OVERRIDE =
#LEAN_OIDC_USERINFO_URL_OVERRIDE =

# optional - override the public key for RSA validation
#LEAN_OIDC_CERTIFICATE_STRING =
#LEAN_OIDC_CERTIFICATE_FILE =

# optional - override the requested scopes
#LEAN_OIDC_SCOPES =

# optional - override the keys used for these fields
#LEAN_OIDC_FIELD_EMAIL =
#LEAN_OIDC_FIELD_FIRSTNAME =
#LEAN_OIDC_FIELD_LASTNAME =

## OpenID Connect setting for github
#LEAN_OIDC_PROVIDER_URL = https://token.actions.githubusercontent.com/
#LEAN_OIDC_AUTH_URL_OVERRIDE = https://github.com/login/oauth/authorize
#LEAN_OIDC_TOKEN_URL_OVERRIDE = https://github.com/login/oauth/access_token
#LEAN_OIDC_USERINFO_URL_OVERRIDE = https://api.github.com/user,https://api.github.com/user/emails
#LEAN_OIDC_SCOPES = user:email
#LEAN_OIDC_FIELD_EMAIL = 0.email

LEAN_RATELIMIT_GENERAL = 50000
LEAN_RATELIMIT_AUTH = 50000
LEAN_RATELIMIT_API = 50000

