

services:
  leantime-dev:
    container_name: leantime-dev
    image: leantime/leantime:latest
    ports:
      - "8090:8080" #Using port 8090 as mentioned in docs, mapping to container's 8080 port
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      LEAN_DB_HOST: 'leantime-db'
      LEAN_DB_USER: 'leantime'
      LEAN_DB_PASSWORD: 'leantime'
      LEAN_DB_DATABASE: 'leantime'
      LEAN_DB_PORT: '3306'
      LEAN_APP_URL: 'http://localhost:8090'
    volumes:
      - "../:/var/www/html"

    depends_on:
      leantime-db:
        condition: service_healthy
    networks:
      - leantime_internal
#      - traefik
#    labels:
#      - "traefik.enable=true"
#      - "traefik.priority=2"
#      - "traefik.docker.network=traefik"
#
#      # HTTP Router
#      - "traefik.http.routers.leantime.rule=Host(`leantime.docker.local`)"
#      - "traefik.http.services.leantime.loadbalancer.server.port=8080"
#      - "traefik.http.routers.leantime.tls=true"


  leantime-redis:
    container_name: leantime-redis
    image: redis:4.0
    ports:
      - "6379"
    networks:
      - leantime_internal
  leantime-db:
    image: mysql:8.4
    ports:
      - "3306"
    environment:
      MYSQL_ROOT_PASSWORD: 'leantime'
      MYSQL_USER: 'leantime'
      MYSQL_PASSWORD: 'leantime'
      MYSQL_DATABASE: 'leantime'
    networks:
      - leantime_internal
    volumes:
      - leantime-mysql:/var/lib/mysql:rw
    healthcheck:
      test: "mysql -u$$MYSQL_USER -p$$MYSQL_PASSWORD -e 'SELECT 1'"
      interval: 5s
      timeout: 5s
      retries: 20

  selenium:
    image: selenium/standalone-chromium
    shm_size: 2gb
    environment:
      - SCREEN_WIDTH=2560
      - SCREEN_HEIGHT=1440
    ports:
      - "4444"
      - "7900"
    networks:
      - leantime_internal
  maildev:
    image: maildev/maildev
    environment:
      - MAILDEV_SMTP_PORT=465
      - MAILDEV_WEB_PORT=8081
    ports:
      - "8081"
    networks:
      - leantime_internal
  s3ninja:
    image: scireum/s3-ninja
    ports:
      - "8083"
    networks:
      - leantime_internal
    volumes:
      - s3ninja-data:/home/<USER>/data


networks:
  leantime_internal:
    driver: bridge
    name: leantime_internal
#  traefik:
#    external: true
#    name: traefik

volumes:
  leantime-mysql:
  s3ninja-data:
