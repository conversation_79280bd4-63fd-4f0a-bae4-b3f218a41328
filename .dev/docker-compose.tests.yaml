#
# This docker compose file is used to build the environment for our testing framework in codeception
# It is not to be used for development
#
services:
  leantime-dev:
    volumes:
      - "../:/var/www/html"
      - "./xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini"
      - "./error_reporting.ini:/usr/local/etc/php/conf.d/error_reporting.ini"
      - "./test.env:/var/www/html/config/.env"
    ports:
      - "8002:8080"
      - "44302:443"
    environment:
      LEAN_ENV: 'testing'
      LEAN_DB_HOST: 'leantime-db'
      LEAN_DB_DATABASE: 'leantime_test'
    env_file:
      - test.env
    depends_on:
      leantime-db:
        condition: service_healthy

  leantime-db:
    environment:
      MYSQL_DATABASE: leantime_test
