<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="InertiaPackage">
    <option name="directoryPaths">
      <list />
    </option>
  </component>
  <component name="LaravelIdeaMainSettings">
    <option name="appNamespace" value="Leantime" />
    <option name="bladeComponentViewsDirectory" value="app/Views/Templates/components" />
    <option name="codeGeneration">
      <LaravelCodeGeneration>
        <option name="addPhpDocs" value="true" />
      </LaravelCodeGeneration>
    </option>
    <option name="controllersBaseClass" value="app\Core\Controller\Controller" />
    <option name="frameworkFound" value="true" />
    <option name="moduleSystemName" value="directory" />
    <option name="runConsole">
      <RunConsoleSettings>
        <option name="artisanPrefix" value="php bin\leantime" />
      </RunConsoleSettings>
    </option>
    <option name="userClassName" value="\Leantime\User" />
    <option name="views">
      <LaravelViews>
        <option name="defaultViewsPath" value="app/Views/Templates" />
      </LaravelViews>
    </option>
  </component>
</project>