<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/app/Plugins/Billing/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/app" isTestSource="false" packagePrefix="Leantime\" />
      <sourceFolder url="file://$MODULE_DIR$/tests/Unit" isTestSource="true" packagePrefix="Unit\" />
      <sourceFolder url="file://$MODULE_DIR$/.phpstan" isTestSource="true" packagePrefix="<PERSON><PERSON><PERSON>\" />
      <sourceFolder url="file://$MODULE_DIR$/app/Plugins/Billing/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/app/Plugins/McpServer/vendor/php-mcp/server/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/html-to-markdown" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bacon/bacon-qr-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/htmlawed/htmlawed" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dasprid/enum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/options-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/robthree/twofactorauth" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/endroid/qr-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/pinyin" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/meyfa/php-svg" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lasserafn/php-initial-avatar-generator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lasserafn/php-string-script-language" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lasserafn/php-initials" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/csv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/kamermans/guzzle-oauth2-subscriber" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mpdf/psr-log-aware-trait" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/pipeline" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/bus" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/support" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/view" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/macroable" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/collections" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/events" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/metasyntactical/composer-plugin-license-check" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/conditionable" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vedmant/laravel-feed-reader" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/simplepie/simplepie" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/session" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/http" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/carbon-cli/carbon-cli" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/simple-cli/simple-cli" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/encryption" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/redis" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/illuminate/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/clue/stream-filter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/http-interop/http-factory-guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/jean85/pretty-package-versions" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/php-http/client-common" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/php-http/discovery" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/php-http/httplug" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/php-http/message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/php-http/promise" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sentry/sentry" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/app/Plugins/Billing/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/app/Plugins/Billing/vendor/stripe/stripe-php" />
      <excludeFolder url="file://$MODULE_DIR$/app/Plugins/CustomFields/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/vendor" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/_laravel_idea" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>