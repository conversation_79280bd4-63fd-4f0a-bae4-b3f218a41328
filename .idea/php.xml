<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="BladeDebugConfiguration">
    <cachePath>$PROJECT_DIR$/storage/framework/views</cachePath>
  </component>
  <component name="LaravelPint">
    <laravel_pint_settings>
      <LaravelPintConfiguration tool_path="$PROJECT_DIR$/vendor/bin/pint" />
      <laravel_pint_by_interpreter asDefaultInterpreter="true" interpreter_id="a06551ca-6eca-4b0a-b3f8-cfeb27b9b34e" tool_path="$PROJECT_DIR$/vendor/bin/pint">
        <option name="timeout" value="30000" />
      </laravel_pint_by_interpreter>
    </laravel_pint_settings>
  </component>
  <component name="LaravelPintOptionsConfiguration">
    <option name="pintJsonPath" value="$PROJECT_DIR$/.pint/pint.json" />
  </component>
  <component name="MessDetector">
    <phpmd_settings>
      <phpmd_by_interpreter asDefaultInterpreter="true" interpreter_id="a06551ca-6eca-4b0a-b3f8-cfeb27b9b34e" timeout="30000" />
    </phpmd_settings>
  </component>
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="codingStandard" value="Custom" />
    <option name="customRuleset" value="$PROJECT_DIR$/phpcs.xml" />
    <option name="showSniffs" value="true" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCSFixer">
    <phpcsfixer_settings>
      <PhpCSFixerConfiguration standards="PSR1;PSR2;Symfony;DoctrineAnnotation;PHP70Migration;PHP71Migration" tool_path="$PROJECT_DIR$/vendor/squizlabs/php_codesniffer/bin/phpcs" timeout="10000" />
      <phpcs_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="a06551ca-6eca-4b0a-b3f8-cfeb27b9b34e" standards="PSR1;PSR2;Symfony;DoctrineAnnotation;PHP70Migration;PHP71Migration" tool_path="/Volumes/Source/leantime-oss/vendor/squizlabs/php_codesniffer/bin/phpcs" timeout="30000" />
    </phpcsfixer_settings>
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <PhpCSConfiguration beautifier_path="$PROJECT_DIR$/vendor/bin/phpcbf" standards="Modernize;MySource;NormalizedArrays;PEAR;PHPCSUtils;PSR1;PSR12;PSR2;Squiz;Universal;Zend" tool_path="$PROJECT_DIR$/vendor/bin/phpcs" />
      <phpcs_by_interpreter asDefaultInterpreter="true" interpreter_id="a06551ca-6eca-4b0a-b3f8-cfeb27b9b34e" beautifier_path="$PROJECT_DIR$/vendor/bin/phpcbf" standards="Modernize;MySource;NormalizedArrays;PEAR;PHPCSUtils;PSR1;PSR12;PSR2;Squiz;Universal;Zend" tool_path="/Volumes/Source/leantime-oss/vendor/bin/phpcs" timeout="30000" />
    </phpcs_settings>
  </component>
  <component name="PhpExternalFormatter">
    <option name="externalFormatter" value="LARAVEL_PINT" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/dasprid/enum" />
      <path value="$PROJECT_DIR$/vendor/endroid/qr-code" />
      <path value="$PROJECT_DIR$/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/htmlawed/htmlawed" />
      <path value="$PROJECT_DIR$/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/vendor/lasserafn/php-initial-avatar-generator" />
      <path value="$PROJECT_DIR$/vendor/lasserafn/php-initials" />
      <path value="$PROJECT_DIR$/vendor/lasserafn/php-string-script-language" />
      <path value="$PROJECT_DIR$/vendor/league/html-to-markdown" />
      <path value="$PROJECT_DIR$/vendor/meyfa/php-svg" />
      <path value="$PROJECT_DIR$/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/vendor/overtrue/pinyin" />
      <path value="$PROJECT_DIR$/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/vendor/robthree/twofactorauth" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/options-resolver" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/vendor/league/csv" />
      <path value="$PROJECT_DIR$/vendor/kamermans/guzzle-oauth2-subscriber" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/mpdf/psr-log-aware-trait" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/illuminate/contracts" />
      <path value="$PROJECT_DIR$/vendor/illuminate/pipeline" />
      <path value="$PROJECT_DIR$/vendor/illuminate/filesystem" />
      <path value="$PROJECT_DIR$/vendor/illuminate/bus" />
      <path value="$PROJECT_DIR$/vendor/illuminate/support" />
      <path value="$PROJECT_DIR$/vendor/illuminate/container" />
      <path value="$PROJECT_DIR$/vendor/illuminate/view" />
      <path value="$PROJECT_DIR$/vendor/illuminate/macroable" />
      <path value="$PROJECT_DIR$/vendor/illuminate/collections" />
      <path value="$PROJECT_DIR$/vendor/illuminate/events" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/illuminate/contracts" />
      <path value="$PROJECT_DIR$/vendor/illuminate/pipeline" />
      <path value="$PROJECT_DIR$/vendor/illuminate/filesystem" />
      <path value="$PROJECT_DIR$/vendor/illuminate/bus" />
      <path value="$PROJECT_DIR$/vendor/illuminate/support" />
      <path value="$PROJECT_DIR$/vendor/illuminate/container" />
      <path value="$PROJECT_DIR$/vendor/illuminate/view" />
      <path value="$PROJECT_DIR$/vendor/illuminate/macroable" />
      <path value="$PROJECT_DIR$/vendor/illuminate/collections" />
      <path value="$PROJECT_DIR$/vendor/illuminate/events" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/vendor/metasyntactical/composer-plugin-license-check" />
      <path value="$PROJECT_DIR$/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/vendor/illuminate/conditionable" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/vedmant/laravel-feed-reader" />
      <path value="$PROJECT_DIR$/vendor/simplepie/simplepie" />
      <path value="$PROJECT_DIR$/vendor/bacon/bacon-qr-code" />
      <path value="$PROJECT_DIR$/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/brick/math" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/mpdf/mpdf" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/php-http/message-factory" />
      <path value="$PROJECT_DIR$/vendor/setasign/fpdi" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php81" />
      <path value="$PROJECT_DIR$/vendor/illuminate/session" />
      <path value="$PROJECT_DIR$/vendor/illuminate/http" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/vendor/illuminate/console" />
      <path value="$PROJECT_DIR$/vendor/illuminate/cache" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/carbon-cli/carbon-cli" />
      <path value="$PROJECT_DIR$/vendor/simple-cli/simple-cli" />
      <path value="$PROJECT_DIR$/vendor/illuminate/encryption" />
      <path value="$PROJECT_DIR$/vendor/illuminate/redis" />
      <path value="$PROJECT_DIR$/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/vendor/illuminate/log" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/vendor/http-interop/http-factory-guzzle" />
      <path value="$PROJECT_DIR$/vendor/sentry/sentry" />
      <path value="$PROJECT_DIR$/vendor/jean85/pretty-package-versions" />
      <path value="$PROJECT_DIR$/vendor/php-http/client-common" />
      <path value="$PROJECT_DIR$/vendor/clue/stream-filter" />
      <path value="$PROJECT_DIR$/vendor/php-http/promise" />
      <path value="$PROJECT_DIR$/vendor/php-http/discovery" />
      <path value="$PROJECT_DIR$/vendor/php-http/message" />
      <path value="$PROJECT_DIR$/vendor/php-http/httplug" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/_laravel_idea" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/vendor/league/config" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/vendor/sentry/sentry-laravel" />
      <path value="$PROJECT_DIR$/app/Plugins/Billing/vendor/stripe/stripe-php" />
      <path value="$PROJECT_DIR$/app/Plugins/Billing/vendor/composer" />
      <path value="$PROJECT_DIR$/app/Plugins/CustomFields/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/vendor/spatie/icalendar-generator" />
      <path value="$PROJECT_DIR$/vendor/spatie/enum" />
      <path value="$PROJECT_DIR$/vendor/symfony/dotenv" />
      <path value="$PROJECT_DIR$/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/vendor/maximebf/debugbar" />
      <path value="$PROJECT_DIR$/vendor/barryvdh/laravel-debugbar" />
      <path value="$PROJECT_DIR$/vendor/blade-ui-kit/blade-icons" />
      <path value="$PROJECT_DIR$/vendor/codeat3/blade-google-material-design-icons" />
      <path value="$PROJECT_DIR$/vendor/predis/predis" />
      <path value="$PROJECT_DIR$/vendor/laravel/socialite" />
      <path value="$PROJECT_DIR$/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/vendor/litesaml/lightsaml" />
      <path value="$PROJECT_DIR$/vendor/league/oauth1-client" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/gitlab" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/microsoft" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/google" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/saml2" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/authentik" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/manager" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/microsoft-azure" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/github" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/gitea" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/laravelpassport" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/okta" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/auth0" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/keycloak" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/appnet" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/propelauth" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/eduid" />
      <path value="$PROJECT_DIR$/vendor/stripe/stripe-php" />
      <path value="$PROJECT_DIR$/vendor/crispchat/php-crisp-api" />
      <path value="$PROJECT_DIR$/vendor/robrichards/xmlseclibs" />
      <path value="$PROJECT_DIR$/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/vendor/squizlabs/php_codesniffer" />
      <path value="$PROJECT_DIR$/vendor/masterminds/html5" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/dealerdirect/phpcodesniffer-composer-installer" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/phpstan/phpstan" />
      <path value="$PROJECT_DIR$/vendor/softcreatr/jsonpath" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection" />
      <path value="$PROJECT_DIR$/vendor/zebra-north/phpcs-short-types" />
      <path value="$PROJECT_DIR$/vendor/justinrainbow/json-schema" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/vendor/php-webdriver/webdriver" />
      <path value="$PROJECT_DIR$/vendor/codeception/lib-xml" />
      <path value="$PROJECT_DIR$/vendor/codeception/module-laravel" />
      <path value="$PROJECT_DIR$/vendor/codeception/lib-asserts" />
      <path value="$PROJECT_DIR$/vendor/codeception/lib-web" />
      <path value="$PROJECT_DIR$/vendor/codeception/codeception" />
      <path value="$PROJECT_DIR$/vendor/codeception/module-asserts" />
      <path value="$PROJECT_DIR$/vendor/codeception/module-db" />
      <path value="$PROJECT_DIR$/vendor/codeception/module-webdriver" />
      <path value="$PROJECT_DIR$/vendor/codeception/lib-innerbrowser" />
      <path value="$PROJECT_DIR$/vendor/codeception/stub" />
      <path value="$PROJECT_DIR$/vendor/codeception/module-rest" />
      <path value="$PROJECT_DIR$/vendor/codeception/module-phpbrowser" />
      <path value="$PROJECT_DIR$/vendor/leantime/leantime-documentor" />
      <path value="$PROJECT_DIR$/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/vendor/symfony/browser-kit" />
      <path value="$PROJECT_DIR$/vendor/phpcsstandards/phpcsextra" />
      <path value="$PROJECT_DIR$/vendor/symfony/dom-crawler" />
      <path value="$PROJECT_DIR$/vendor/phpcsstandards/phpcsutils" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/symfony/filesystem" />
      <path value="$PROJECT_DIR$/vendor/behat/gherkin" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/league/uri" />
      <path value="$PROJECT_DIR$/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/vendor/php-http/guzzle7-adapter" />
      <path value="$PROJECT_DIR$/vendor/litesaml/schemas" />
      <path value="$PROJECT_DIR$/vendor/phpstan/extension-installer" />
      <path value="$PROJECT_DIR$/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/oauth-subscriber" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/vendor/prism-php/prism" />
      <path value="$PROJECT_DIR$/vendor/marc-mabe/php-enum" />
      <path value="$PROJECT_DIR$/vendor/inspector-apm/neuron-ai" />
      <path value="$PROJECT_DIR$/vendor/johngrogg/ics-parser" />
      <path value="$PROJECT_DIR$/vendor/sabre/dav" />
      <path value="$PROJECT_DIR$/vendor/sabre/vobject" />
      <path value="$PROJECT_DIR$/vendor/sabre/xml" />
      <path value="$PROJECT_DIR$/vendor/sabre/http" />
      <path value="$PROJECT_DIR$/vendor/sabre/uri" />
      <path value="$PROJECT_DIR$/vendor/sabre/event" />
      <path value="$PROJECT_DIR$/vendor/hkulekci/qdrant" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem-aws-s3-v3" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem-path-prefixing" />
      <path value="$PROJECT_DIR$/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/vendor/react/stream" />
      <path value="$PROJECT_DIR$/vendor/react/event-loop" />
      <path value="$PROJECT_DIR$/vendor/react/promise" />
      <path value="$PROJECT_DIR$/vendor/php-mcp/server" />
      <path value="$PROJECT_DIR$/vendor/opis/string" />
      <path value="$PROJECT_DIR$/vendor/evenement/evenement" />
      <path value="$PROJECT_DIR$/vendor/opis/uri" />
      <path value="$PROJECT_DIR$/vendor/opis/json-schema" />
      <path value="$PROJECT_DIR$/vendor/php-mcp/laravel" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/php-mcp/schema" />
    </include_path>
  </component>
  <component name="PhpProjectServersManager">
    <servers>
      <server host="leantime.localhost" id="822abf0f-3bb4-4146-b5e4-26439962d6a2" name="Mamp Localhost" port="443">
        <path_mappings>
          <mapping local-root="$PROJECT_DIR$" remote-root="/var/www/html" />
        </path_mappings>
      </server>
    </servers>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.2">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpStan">
    <PhpStan_settings>
      <PhpStanConfiguration tool_path="$PROJECT_DIR$/vendor/bin/phpstan" />
      <phpstan_by_interpreter asDefaultInterpreter="true" interpreter_id="a06551ca-6eca-4b0a-b3f8-cfeb27b9b34e" tool_path="$PROJECT_DIR$/vendor/bin/phpstan" timeout="60000" />
    </PhpStan_settings>
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="autoload" value="$PROJECT_DIR$/vendor/autoload.php" />
    <option name="config" value="$PROJECT_DIR$/.phpstan/phpstan.neon" />
    <option name="level" value="1" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings custom_loader_path="$PROJECT_DIR$/vendor/autoload.php" phpunit_phar_path="" />
    </phpunit_settings>
  </component>
  <component name="Psalm">
    <Psalm_settings>
      <psalm_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="a06551ca-6eca-4b0a-b3f8-cfeb27b9b34e" timeout="60000" />
    </Psalm_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="TwigDebugConfiguration">
    <cachePath />
  </component>
</project>