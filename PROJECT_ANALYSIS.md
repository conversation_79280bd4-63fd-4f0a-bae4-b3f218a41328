# Leantime 项目架构分析文档

## 📋 项目概述

**项目名称**: Leantime
**项目类型**: 开源项目管理系统
**版本**: 3.5.12
**许可证**: AGPL-3.0-only
**设计理念**: 为非项目管理专业人员设计，简单如Trello，功能如Jira，支持神经多样性
**主要特性**: 任务管理、看板、甘特图、敏捷开发、精益管理、创新管理

### 项目定位
- 🎯 **目标用户**: 非专业项目经理、小团队、创业公司
- 🧠 **神经多样性友好**: 专为ADHD、阅读障碍、自闭症用户设计
- 🔄 **替代方案**: ClickUp、Monday、Asana的开源替代品
- 💪 **功能定位**: 简单如Trello，强大如Jira

## 🏗️ 系统架构

Leantime采用现代PHP框架Laravel为基础，实现了分层架构和领域驱动设计(DDD)模式：

### 架构特点
- **分层架构**: 清晰的UI层、中间件层、核心层、业务领域层、服务层和存储层
- **领域驱动设计**: 按业务领域组织代码，每个域包含Controllers、Services、Repositories、Models
- **插件系统**: 支持插件扩展，模块化设计
- **事件驱动**: 基于Laravel事件系统，支持松耦合的组件交互

### 核心组件
- **Bootloader**: 应用启动器，管理整个应用生命周期
- **HttpKernel**: HTTP请求处理内核
- **ConsoleKernel**: 命令行处理内核
- **中间件系统**: 认证、会话、限流等中间件处理

## 🛠️ 技术栈详解

### 后端技术栈
- **PHP 8.2+**: 采用现代PHP特性，支持类型声明、属性等
- **Laravel 11**: 最新版Laravel框架，提供完整的Web开发生态
- **Composer**: PHP依赖管理，遵循PSR标准

### 前端技术栈
- **HTML5 + CSS3**: 现代Web标准，响应式设计
- **JavaScript**: 原生JS结合jQuery，轻量级前端交互
- **LESS**: CSS预处理器，模块化样式管理
- **Make + NPM**: 资源构建和依赖管理

### 核心依赖包
| 包名 | 用途 | 版本 |
|------|------|------|
| laravel/framework | Web应用框架 | ^v11.44 |
| guzzlehttp/guzzle | HTTP客户端 | ^7.9 |
| phpmailer/phpmailer | 邮件发送 | ^6.6 |
| aws/aws-sdk-php | AWS服务集成 | ^3.344.6 |
| laravel/sanctum | API认证 | ^4.0 |
| laravel/socialite | 第三方登录 | ^5.16 |
| robthree/twofactorauth | 双因子认证 | ^1.8 |
| endroid/qr-code | 二维码生成 | ^6.0 |

### 认证与安全
- **Laravel Sanctum**: API Token认证
- **Laravel Socialite**: 支持GitHub、Google、Microsoft等第三方登录
- **LDAP/Active Directory**: 企业级用户目录集成
- **OpenID Connect**: 现代统一认证协议
- **双因子认证**: TOTP算法，支持Google Authenticator
- **SAML 2.0**: 企业级单点登录

### 数据存储
- **MySQL 8.4**: 主数据库，存储用户、项目、任务等核心数据
- **Redis 4.0**: 缓存和会话存储，提升性能
- **AWS S3**: 对象存储，支持文件上传和管理
- **League Flysystem**: 文件系统抽象，支持多种存储后端

## 📊 系统架构图

### 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        UI[用户界面]
        JS[JavaScript/jQuery]
        CSS[TailwindCSS]
    end

    subgraph "中间件层"
        Auth[认证中间件]
        Session[会话管理]
        CORS[跨域处理]
        Rate[限流控制]
    end

    subgraph "应用层"
        Boot[Bootloader]
        HTTP[HttpKernel]
        Console[ConsoleKernel]
        Router[路由系统]
    end

    subgraph "业务领域层"
        Projects[项目管理]
        Tickets[任务管理]
        Users[用户管理]
        Canvas[画布工具]
        Files[文件管理]
        Reports[报表分析]
    end

    subgraph "服务层"
        ProjectSvc[项目服务]
        TicketSvc[任务服务]
        UserSvc[用户服务]
        AuthSvc[认证服务]
        FileSvc[文件服务]
    end

    subgraph "数据访问层"
        ProjectRepo[项目仓储]
        TicketRepo[任务仓储]
        UserRepo[用户仓储]
        FileRepo[文件仓储]
    end

    subgraph "数据存储层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        S3[(AWS S3存储)]
    end

    UI --> Auth
    Auth --> HTTP
    HTTP --> Router
    Router --> Projects
    Projects --> ProjectSvc
    ProjectSvc --> ProjectRepo
    ProjectRepo --> MySQL

    Tickets --> TicketSvc
    TicketSvc --> TicketRepo
    TicketRepo --> MySQL

    Users --> UserSvc
    UserSvc --> UserRepo
    UserRepo --> MySQL

    Files --> FileSvc
    FileSvc --> FileRepo
    FileRepo --> S3

    Session --> Redis
    Rate --> Redis
```

### 数据流架构图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 前端界面
    participant MW as 中间件
    participant Ctrl as 控制器
    participant Svc as 服务层
    participant Repo as 仓储层
    participant DB as 数据库

    User->>UI: 发起请求
    UI->>MW: HTTP请求
    MW->>MW: 认证检查
    MW->>Ctrl: 路由到控制器
    Ctrl->>Svc: 调用业务服务
    Svc->>Svc: 业务逻辑处理
    Svc->>Repo: 数据访问请求
    Repo->>DB: SQL查询
    DB-->>Repo: 返回数据
    Repo-->>Svc: 返回实体对象
    Svc-->>Ctrl: 返回处理结果
    Ctrl-->>UI: 渲染视图
    UI-->>User: 显示结果
```

## 📁 代码结构分析

### 目录结构
```
leantime/
├── app/                    # 应用核心代码
│   ├── Core/              # 框架核心组件
│   │   ├── Application/   # 应用服务提供者
│   │   ├── Auth/         # 认证组件
│   │   ├── Http/         # HTTP处理
│   │   ├── Database/     # 数据库管理
│   │   ├── Events/       # 事件系统
│   │   ├── Middleware/   # 中间件
│   │   └── UI/          # 用户界面组件
│   ├── Domain/           # 业务领域模块
│   │   ├── Auth/        # 认证领域
│   │   ├── Projects/    # 项目管理
│   │   ├── Tickets/     # 任务管理
│   │   ├── Users/       # 用户管理
│   │   ├── Canvas/      # 各种画布工具
│   │   └── ...          # 其他业务领域
│   └── Views/           # 视图模板
├── public/              # Web根目录
│   ├── assets/         # 静态资源
│   └── index.php       # 应用入口
├── config/             # 配置文件
├── bootstrap/          # 启动文件
├── database/           # 数据库迁移
└── tests/              # 测试文件
```

### 业务领域模块
Leantime包含50+个业务领域，主要包括：

**核心业务域**:
- **Auth**: 用户认证与授权
- **Users**: 用户管理
- **Projects**: 项目管理
- **Tickets**: 任务/票据管理
- **Dashboard**: 仪表盘
- **Reports**: 报表分析

**协作工具域**:
- **Comments**: 评论系统
- **Files**: 文件管理
- **Calendar**: 日历功能
- **Notifications**: 通知系统
- **Wiki**: 知识库

**画布工具域**:
- **Leancanvas**: 精益画布
- **Canvas**: 通用画布
- **Valuecanvas**: 价值主张画布
- **Goalcanvas**: 目标设定画布
- **Retroscanvas**: 回顾画布

## 🔍 核心功能深度分析

### 项目管理功能

```mermaid
graph LR
    subgraph "项目生命周期"
        Create[创建项目] --> Plan[项目规划]
        Plan --> Execute[执行管理]
        Execute --> Monitor[监控跟踪]
        Monitor --> Close[项目结束]
    end

    subgraph "项目功能模块"
        Dashboard[项目仪表盘]
        Tasks[任务管理]
        Timeline[时间线]
        Budget[预算管理]
        Team[团队协作]
        Reports[项目报表]
    end

    Create --> Dashboard
    Plan --> Tasks
    Execute --> Timeline
    Monitor --> Reports
```

**核心特性**:
- 📊 **项目仪表盘**: 实时显示项目进度、任务状态、团队绩效
- 📋 **任务管理**: 支持看板、甘特图、列表、日历多种视图
- 💰 **预算跟踪**: 小时预算和金额预算双重控制
- 👥 **团队协作**: 项目成员管理、权限控制、通知系统
- 📈 **进度监控**: 里程碑管理、依赖关系、风险评估

### 任务管理系统

```mermaid
graph TB
    subgraph "任务视图"
        Kanban[看板视图]
        Gantt[甘特图]
        Table[表格视图]
        Calendar[日历视图]
    end

    subgraph "任务属性"
        Priority[优先级]
        Status[状态]
        Assignee[负责人]
        Due[截止日期]
        Tags[标签]
        Dependencies[依赖关系]
    end

    subgraph "任务操作"
        Create[创建任务]
        Edit[编辑任务]
        Move[拖拽移动]
        Comment[添加评论]
        Attach[附件上传]
        Track[时间跟踪]
    end

    Kanban --> Priority
    Gantt --> Dependencies
    Table --> Status
    Calendar --> Due
```

**核心特性**:
- 🎯 **多视图支持**: 看板、甘特图、表格、日历四种视图模式
- 🔄 **拖拽操作**: 直观的任务状态变更和优先级调整
- ⏱️ **时间跟踪**: 内置时间记录功能，支持工时统计
- 🔗 **任务依赖**: 支持复杂的任务依赖关系管理
- 📎 **文件附件**: 任务级别的文件管理和版本控制

### 用户认证与权限系统

```mermaid
graph TB
    subgraph "认证方式"
        Local[本地认证]
        LDAP[LDAP/AD]
        OIDC[OpenID Connect]
        SAML[SAML 2.0]
        Social[社交登录]
    end

    subgraph "权限层级"
        Global[全局权限]
        Project[项目权限]
        Resource[资源权限]
    end

    subgraph "角色类型"
        Owner[系统所有者]
        Admin[管理员]
        Manager[项目经理]
        Editor[编辑者]
        Readonly[只读用户]
    end

    Local --> Global
    LDAP --> Project
    OIDC --> Resource

    Owner --> Admin
    Admin --> Manager
    Manager --> Editor
    Editor --> Readonly
```

**核心特性**:
- 🔐 **多重认证**: 支持本地、LDAP、OIDC、SAML等多种认证方式
- 🛡️ **分层权限**: 全局、项目、资源三级权限控制
- 👤 **角色管理**: 灵活的角色定义和权限分配
- 🔑 **双因子认证**: TOTP算法，增强账户安全性
- 🌐 **单点登录**: 企业级SSO集成支持

## 🔧 开发与部署

## 🗄️ 数据库架构分析

### 核心数据表结构

```mermaid
erDiagram
    zp_user {
        int id PK
        varchar username
        varchar password
        varchar firstname
        varchar lastname
        varchar role
        datetime lastlogin
        varchar status
    }

    zp_projects {
        int id PK
        varchar name
        int clientId FK
        text details
        int state
        varchar hourBudget
        int dollarBudget
        datetime start
        datetime end
        datetime created
    }

    zp_tickets {
        int id PK
        varchar headline
        text description
        int projectId FK
        int editorId FK
        varchar status
        varchar priority
        datetime dateToFinish
        datetime created
    }

    zp_projectassignment {
        int id PK
        int projectId FK
        int userId FK
        varchar projectRole
    }

    zp_comments {
        int id PK
        varchar module
        int moduleId
        int userId FK
        text comment
        datetime date
    }

    zp_files {
        int id PK
        varchar module
        int moduleId
        int userId FK
        varchar realName
        varchar encName
        datetime date
    }

    zp_user ||--o{ zp_projectassignment : "assigned to"
    zp_projects ||--o{ zp_projectassignment : "has members"
    zp_projects ||--o{ zp_tickets : "contains"
    zp_user ||--o{ zp_tickets : "assigned to"
    zp_user ||--o{ zp_comments : "creates"
    zp_user ||--o{ zp_files : "uploads"
```

### 数据访问模式

**Repository模式实现**:
- 每个业务域都有对应的Repository类
- 继承自`Leantime\Core\Db\Repository`基类
- 使用原生SQL查询，计划迁移到Doctrine ORM
- 支持事务处理和连接池管理

**数据模型特点**:
- 使用`zp_`前缀的表命名约定
- 支持软删除和审计日志
- 采用UTF-8编码，支持多语言
- 索引优化，支持大数据量查询

## 🔌 API架构设计

### JSON-RPC API

```mermaid
graph LR
    subgraph "API层"
        Client[客户端] --> Gateway[API网关]
        Gateway --> Auth[认证中间件]
        Auth --> RPC[JSON-RPC处理器]
    end

    subgraph "服务层"
        RPC --> ProjectAPI[项目API]
        RPC --> TicketAPI[任务API]
        RPC --> UserAPI[用户API]
        RPC --> FileAPI[文件API]
    end

    subgraph "业务层"
        ProjectAPI --> ProjectService[项目服务]
        TicketAPI --> TicketService[任务服务]
        UserAPI --> UserService[用户服务]
        FileAPI --> FileService[文件服务]
    end
```

**API特性**:
- 📡 **JSON-RPC 2.0**: 标准化的远程过程调用协议
- 🔐 **Token认证**: Laravel Sanctum提供API Token管理
- 📊 **批量操作**: 支持批量请求和响应
- 🚦 **限流控制**: 防止API滥用和DDoS攻击
- 📝 **自动文档**: 基于注解的API文档生成

### 开发环境
基于Docker Compose的完整开发环境：
- **Leantime应用**: 端口8090
- **MySQL数据库**: 端口63388
- **Redis缓存**: 端口63383
- **MailDev邮件测试**: 端口63387
- **S3Ninja存储模拟**: 端口63384
- **Selenium测试**: 端口63386

### 质量保证
- **自动化测试**: Codeception框架，支持单元测试、集成测试、验收测试
- **代码质量**: PHPStan静态分析，PHP CS代码风格检查
- **代码格式**: Laravel Pint自动格式化
- **错误处理**: Whoops美化错误页面，Sentry错误监控

### 部署选项
1. **Docker容器化部署**: 推荐的生产部署方式
2. **Kubernetes编排**: 提供Helm Charts，支持云原生部署
3. **传统虚拟主机**: 支持标准LAMP环境部署

## 📊 性能与扩展性

### 缓存策略
- **Redis会话存储**: 支持横向扩展和会话共享
- **数据库查询缓存**: Laravel内置缓存系统
- **静态资源缓存**: CDN友好的资源管理

### 扩展能力
- **水平扩展**: 支持多实例负载均衡
- **数据库读写分离**: MySQL主从复制支持
- **文件存储扩展**: 支持S3兼容的对象存储
- **插件系统**: 模块化扩展架构

## 🔌 插件系统架构

### 插件生态系统

```mermaid
graph TB
    subgraph "插件管理"
        Registry[插件注册表]
        Loader[插件加载器]
        Manager[插件管理器]
    end

    subgraph "插件类型"
        UI[界面插件]
        Service[服务插件]
        Integration[集成插件]
        Theme[主题插件]
    end

    subgraph "插件API"
        Hooks[钩子系统]
        Events[事件系统]
        Filters[过滤器]
        Actions[动作处理]
    end

    Registry --> Loader
    Loader --> Manager
    Manager --> UI
    Manager --> Service

    UI --> Hooks
    Service --> Events
    Integration --> Filters
    Theme --> Actions
```

**插件特性**:
- 🔧 **热插拔**: 支持运行时安装和卸载插件
- 🎨 **主题系统**: 可定制的界面主题和样式
- 🔗 **集成插件**: 支持第三方服务集成（Slack、Teams等）
- 📦 **包管理**: Composer集成的插件包管理
- 🛡️ **安全沙箱**: 插件运行环境隔离

### 插件开发框架
- **标准化API**: 统一的插件开发接口
- **生命周期管理**: 插件安装、激活、停用、卸载
- **依赖管理**: 插件间依赖关系处理
- **版本控制**: 插件版本兼容性检查

## 🔒 安全特性

### 认证机制
- **多因子认证**: TOTP、短信验证
- **企业集成**: LDAP、SAML、OIDC
- **API安全**: Sanctum Token认证，速率限制

### 数据保护
- **加密传输**: 强制HTTPS，TLS加密
- **数据加密**: 敏感数据库字段加密
- **访问控制**: 基于角色的权限管理
- **审计日志**: 完整的用户操作审计

## 🎯 核心优势

1. **易用性**: 为非专业项目经理设计，学习成本低
2. **神经多样性友好**: 考虑不同认知模式的用户需求
3. **功能完整**: 涵盖项目管理全生命周期
4. **技术先进**: 基于现代PHP框架，架构清晰
5. **扩展性强**: 插件系统和模块化设计
6. **部署灵活**: 支持多种部署方式
7. **开源免费**: AGPL-3.0许可证，社区驱动

## 📈 发展趋势

### 技术演进
- **现代化框架**: 基于Laravel 11最新特性
- **容器化优先**: Docker/Kubernetes原生支持
- **API优先**: RESTful API设计，支持移动端集成
- **云原生**: 支持各大云平台部署

### 功能发展
- **AI集成**: 智能项目分析和建议
- **实时协作**: WebSocket支持实时更新
- **移动优化**: 响应式设计，PWA支持
- **企业级功能**: 高级报表、SSO、合规性

## 🔗 相关资源

- **官方网站**: https://leantime.io
- **文档中心**: https://docs.leantime.io
- **GitHub仓库**: https://github.com/Leantime/leantime
- **社区支持**: Discord社区
- **商业支持**: 企业级支持服务

---

## 📋 项目评估总结

### 技术成熟度评估

| 维度 | 评分 | 说明 |
|------|------|------|
| 架构设计 | ⭐⭐⭐⭐⭐ | 清晰的分层架构，领域驱动设计 |
| 代码质量 | ⭐⭐⭐⭐ | 良好的代码规范，完善的测试覆盖 |
| 文档完整性 | ⭐⭐⭐⭐ | 详细的开发文档和API文档 |
| 社区活跃度 | ⭐⭐⭐⭐ | 活跃的GitHub社区，定期更新 |
| 扩展性 | ⭐⭐⭐⭐⭐ | 强大的插件系统，模块化设计 |
| 安全性 | ⭐⭐⭐⭐ | 多重认证，完善的权限控制 |
| 性能表现 | ⭐⭐⭐⭐ | 良好的缓存策略，支持横向扩展 |

### 竞争优势分析

**相比传统项目管理工具**:
- ✅ **开源免费**: 无许可费用，可自由定制
- ✅ **神经多样性友好**: 独特的用户体验设计
- ✅ **技术先进**: 基于现代PHP框架，架构清晰
- ✅ **部署灵活**: 支持云端和本地部署

**相比其他开源方案**:
- ✅ **功能完整**: 涵盖项目管理全生命周期
- ✅ **易于使用**: 为非专业用户设计
- ✅ **活跃维护**: 定期更新，社区支持良好
- ✅ **企业级特性**: 支持SSO、LDAP等企业需求

### 适用场景推荐

**最适合的使用场景**:
- 🎯 中小型团队项目管理
- 🏢 初创公司和敏捷团队
- 🎓 教育机构项目教学
- 🔬 研发团队创新管理
- 🏥 需要神经多样性支持的组织

**不太适合的场景**:
- ❌ 超大型企业级项目（>1000用户）
- ❌ 需要复杂工作流的制造业
- ❌ 严格合规要求的金融行业
- ❌ 需要深度定制的特殊行业

---

**分析完成时间**: 2025-08-22
**分析版本**: 基于主分支最新代码
**维护状态**: ✅ 活跃开发中
**推荐指数**: ⭐⭐⭐⭐⭐ (5/5)
